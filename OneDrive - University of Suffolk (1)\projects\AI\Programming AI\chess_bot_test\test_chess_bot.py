#!/usr/bin/env python3
"""
Unit tests for the chess bot implementation.
"""

import unittest
import chess
from chess_bot import ChessBot
from chess_game import ChessGame

class TestChessBot(unittest.TestCase):
    """Test cases for the ChessBot class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.bot = ChessBot(depth=2, name="TestBot")
        self.board = chess.Board()
    
    def test_bot_initialization(self):
        """Test bot initialization."""
        self.assertEqual(self.bot.depth, 2)
        self.assertEqual(self.bot.name, "TestBot")
        self.assertIsInstance(self.bot.piece_values, dict)
    
    def test_board_evaluation_starting_position(self):
        """Test board evaluation at starting position."""
        score = self.bot.evaluate_board(self.board)
        # Starting position should be roughly equal
        self.assertAlmostEqual(score, 0, delta=100)
    
    def test_board_evaluation_checkmate(self):
        """Test board evaluation in checkmate position."""
        # Set up a simple checkmate position
        self.board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/6P1/5P2/PPPPP2P/RNBQKBNR b KQkq - 0 2")
        self.board.push_san("Qh4#")  # Fool's mate
        
        score = self.bot.evaluate_board(self.board)
        # Should heavily favor the winning side
        self.assertGreater(abs(score), 10000)
    
    def test_get_best_move_returns_legal_move(self):
        """Test that get_best_move returns a legal move."""
        move = self.bot.get_best_move(self.board)
        self.assertIsNotNone(move)
        self.assertIn(move, self.board.legal_moves)
    
    def test_minimax_depth_zero(self):
        """Test minimax with depth 0."""
        score, move = self.bot.minimax(self.board, 0, float('-inf'), float('inf'), True)
        self.assertIsInstance(score, int)
        # At depth 0, move should be None (just evaluation)
        self.assertIsNone(move)
    
    def test_set_depth(self):
        """Test setting bot depth."""
        original_depth = self.bot.depth
        new_depth = 4
        self.bot.set_depth(new_depth)
        self.assertEqual(self.bot.depth, new_depth)
        self.assertNotEqual(self.bot.depth, original_depth)
    
    def test_piece_values(self):
        """Test that piece values are reasonable."""
        # Queen should be worth more than a rook
        self.assertGreater(self.bot.piece_values[chess.QUEEN], 
                          self.bot.piece_values[chess.ROOK])
        
        # Rook should be worth more than a bishop
        self.assertGreater(self.bot.piece_values[chess.ROOK], 
                          self.bot.piece_values[chess.BISHOP])
        
        # Bishop should be worth more than a knight (roughly equal but bishop slightly higher)
        self.assertGreaterEqual(self.bot.piece_values[chess.BISHOP], 
                               self.bot.piece_values[chess.KNIGHT])
        
        # Knight should be worth more than a pawn
        self.assertGreater(self.bot.piece_values[chess.KNIGHT], 
                          self.bot.piece_values[chess.PAWN])
    
    def test_bot_prefers_captures(self):
        """Test that bot prefers capturing moves when beneficial."""
        # Set up a position where a capture is clearly beneficial
        self.board.set_fen("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2")
        
        # Bot should find a good move
        move = self.bot.get_best_move(self.board)
        self.assertIsNotNone(move)
        self.assertIn(move, self.board.legal_moves)


class TestChessGame(unittest.TestCase):
    """Test cases for the ChessGame class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.game = ChessGame()
    
    def test_game_initialization(self):
        """Test game initialization."""
        self.assertIsInstance(self.game.board, chess.Board)
        self.assertIsInstance(self.game.bot, ChessBot)
        self.assertEqual(self.game.human_color, chess.WHITE)
        self.assertEqual(len(self.game.game_history), 0)
    
    def test_make_move_valid(self):
        """Test making a valid move."""
        move = chess.Move.from_uci("e2e4")
        result = self.game.make_move(move)
        self.assertTrue(result)
        self.assertEqual(len(self.game.game_history), 1)
        self.assertEqual(self.game.game_history[0], "e4")
    
    def test_switch_colors(self):
        """Test switching player colors."""
        original_color = self.game.human_color
        self.game.switch_colors()
        self.assertNotEqual(self.game.human_color, original_color)
    
    def test_reset_game(self):
        """Test resetting the game."""
        # Make a move first
        move = chess.Move.from_uci("e2e4")
        self.game.make_move(move)
        
        # Reset and check
        self.game.reset_game()
        self.assertTrue(self.game.board.is_valid())
        self.assertEqual(len(self.game.game_history), 0)
        self.assertEqual(str(self.game.board), str(chess.Board()))


class TestChessIntegration(unittest.TestCase):
    """Integration tests for the complete chess system."""
    
    def test_complete_game_simulation(self):
        """Test a complete short game simulation."""
        bot1 = ChessBot(depth=1, name="Bot1")
        bot2 = ChessBot(depth=1, name="Bot2")
        board = chess.Board()
        
        moves_played = 0
        max_moves = 20  # Limit for test
        
        while not board.is_game_over() and moves_played < max_moves:
            current_bot = bot1 if board.turn == chess.WHITE else bot2
            move = current_bot.get_best_move(board)
            
            self.assertIsNotNone(move)
            self.assertIn(move, board.legal_moves)
            
            board.push(move)
            moves_played += 1
        
        # Game should have progressed
        self.assertGreater(moves_played, 0)
    
    def test_bot_performance_consistency(self):
        """Test that bot returns consistent moves for the same position."""
        bot = ChessBot(depth=2, name="ConsistencyBot")
        board = chess.Board()
        
        # Get move twice for the same position
        move1 = bot.get_best_move(board)
        move2 = bot.get_best_move(board)
        
        # Should return the same move (deterministic at same depth)
        self.assertEqual(move1, move2)


def run_performance_test():
    """Run a performance test to check bot speed."""
    print("\nRunning performance test...")
    
    bot = ChessBot(depth=3, name="PerfBot")
    board = chess.Board()
    
    import time
    start_time = time.time()
    
    # Test 5 moves
    for i in range(5):
        move = bot.get_best_move(board)
        if move:
            board.push(move)
        else:
            break
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"Performance test completed:")
    print(f"- 5 moves calculated in {total_time:.2f} seconds")
    print(f"- Average time per move: {total_time/5:.2f} seconds")
    print(f"- Total nodes evaluated: {bot.nodes_evaluated}")


if __name__ == "__main__":
    # Run unit tests
    print("Running Chess Bot Unit Tests...")
    unittest.main(verbosity=2, exit=False)
    
    # Run performance test
    run_performance_test()
