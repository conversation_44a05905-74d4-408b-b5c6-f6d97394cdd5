PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python test_chess_bot.py
Running Chess Bot Unit Tests...
test_board_evaluation_checkmate (__main__.TestChessBot.test_board_evaluation_checkmate)
Test board evaluation in checkmate position. ... ok
test_board_evaluation_starting_position (__main__.TestChessBot.test_board_evaluation_starting_position)
Test board evaluation at starting position. ... ok
test_bot_initialization (__main__.TestChessBot.test_bot_initialization)
Test bot initialization. ... ok
test_bot_prefers_captures (__main__.TestChessBot.test_bot_prefers_captures)
Test that bot prefers capturing moves when beneficial. ... TestBot evaluated 143 positions in 0.04 seconds
ok
test_get_best_move_returns_legal_move (__main__.TestChessBot.test_get_best_move_returns_legal_move)
Test that get_best_move returns a legal move. ... TestBot evaluated 80 positions in 0.03 seconds
ok
test_minimax_depth_zero (__main__.TestChessBot.test_minimax_depth_zero)
Test minimax with depth 0. ... ok
test_piece_values (__main__.TestChessBot.test_piece_values)
Test that piece values are reasonable. ... ok
test_set_depth (__main__.TestChessBot.test_set_depth)
Test setting bot depth. ... TestBot search depth set to 4
ok
test_game_initialization (__main__.TestChessGame.test_game_initialization)
Test game initialization. ... ok
test_make_move_valid (__main__.TestChessGame.test_make_move_valid)
Test making a valid move. ... ok
test_reset_game (__main__.TestChessGame.test_reset_game)
Test resetting the game. ... Game reset to starting position.
ok
test_switch_colors (__main__.TestChessGame.test_switch_colors)
Test switching player colors. ... You are now playing as Black
ok
test_bot_performance_consistency (__main__.TestChessIntegration.test_bot_performance_consistency)
Test that bot returns consistent moves for the same position. ... ConsistencyBot evaluated 80 positions in 0.04 seconds
ConsistencyBot evaluated 80 positions in 0.09 seconds
ok
test_complete_game_simulation (__main__.TestChessIntegration.test_complete_game_simulation)
Test a complete short game simulation. ... Bot1 evaluated 21 positions in 0.01 seconds
Bot2 evaluated 21 positions in 0.02 seconds
Bot1 evaluated 23 positions in 0.01 seconds
Bot2 evaluated 23 positions in 0.02 seconds
Bot1 evaluated 25 positions in 0.05 seconds
Bot2 evaluated 24 positions in 0.03 seconds
Bot1 evaluated 22 positions in 0.01 seconds
Bot2 evaluated 26 positions in 0.02 seconds
Bot1 evaluated 19 positions in 0.01 seconds
Bot2 evaluated 29 positions in 0.03 seconds
Bot1 evaluated 3 positions in 0.00 seconds
Bot2 evaluated 24 positions in 0.01 seconds
Bot1 evaluated 22 positions in 0.02 seconds
Bot2 evaluated 21 positions in 0.01 seconds
Bot1 evaluated 33 positions in 0.01 seconds
Bot2 evaluated 18 positions in 0.01 seconds
Bot1 evaluated 37 positions in 0.09 seconds
Bot2 evaluated 2 positions in 0.00 seconds
Bot1 evaluated 19 positions in 0.03 seconds
Bot2 evaluated 22 positions in 0.01 seconds
ok

----------------------------------------------------------------------
Ran 14 tests in 2.608s

OK

Running performance test...
PerfBot evaluated 715 positions in 0.25 seconds
PerfBot evaluated 841 positions in 0.32 seconds
PerfBot evaluated 761 positions in 0.23 seconds
PerfBot evaluated 2300 positions in 1.27 seconds
PerfBot evaluated 1634 positions in 1.14 seconds
Performance test completed:
- 5 moves calculated in 3.41 seconds
- Average time per move: 0.68 seconds
- Total nodes evaluated: 1634