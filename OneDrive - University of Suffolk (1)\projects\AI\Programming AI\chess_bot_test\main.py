#!/usr/bin/env python3
"""
Chess Bot - A command-line chess game with AI opponent
"""

import sys
import chess
from chess_game import ChessGame
from chess_bot import ChessBot
from utils import format_board_unicode, analyze_position, format_analysis

def show_main_menu():
    """Display the main menu options."""
    print("\n" + "="*50)
    print("CHESS BOT - MAIN MENU")
    print("="*50)
    print("1. Play against ChessBot")
    print("2. <PERSON><PERSON> vs <PERSON><PERSON> (watch AI play itself)")
    print("3. Set bot difficulty")
    print("4. Position analysis demo")
    print("5. Help")
    print("6. Quit")
    print("="*50)

def show_help():
    """Display help information."""
    print("\n" + "="*50)
    print("CHESS BOT HELP")
    print("="*50)
    print("This is a chess game where you can play against an AI opponent.")
    print()
    print("FEATURES:")
    print("- Full chess rules implementation")
    print("- AI opponent using minimax algorithm")
    print("- Multiple difficulty levels")
    print("- Standard algebraic notation support")
    print("- Move validation and game state detection")
    print()
    print("DURING GAME:")
    print("- Enter moves in algebraic notation (e.g., 'e4', 'Nf3', 'O-O')")
    print("- Type 'help' for move format examples")
    print("- Type 'moves' to see all legal moves")
    print("- Type 'quit' to exit the game")
    print()
    print("DIFFICULTY LEVELS:")
    print("- Easy (depth 2): Quick moves, basic strategy")
    print("- Medium (depth 3): Balanced play (default)")
    print("- Hard (depth 4): Strong tactical play")
    print("- Expert (depth 5): Very strong play (slower)")
    print("="*50)

def set_difficulty():
    """Allow user to set bot difficulty."""
    print("\n" + "="*40)
    print("SET DIFFICULTY")
    print("="*40)
    print("1. Easy (depth 2) - Quick moves")
    print("2. Medium (depth 3) - Default")
    print("3. Hard (depth 4) - Strong play")
    print("4. Expert (depth 5) - Very strong")
    print("="*40)
    
    while True:
        try:
            choice = input("Select difficulty (1-4): ").strip()
            
            if choice == '1':
                return 2, "Easy"
            elif choice == '2':
                return 3, "Medium"
            elif choice == '3':
                return 4, "Hard"
            elif choice == '4':
                return 5, "Expert"
            else:
                print("Invalid choice. Please enter 1-4.")
        except KeyboardInterrupt:
            return None, None

def bot_vs_bot_game():
    """Run a bot vs bot game for demonstration."""
    print("\n" + "="*50)
    print("BOT VS BOT DEMONSTRATION")
    print("="*50)
    print("Watch two AI bots play against each other!")
    print("Press Ctrl+C to stop the game early.")
    print()
    
    # Create two bots with different depths
    white_bot = ChessBot(depth=3, name="WhiteBot")
    black_bot = ChessBot(depth=3, name="BlackBot")

    board = chess.Board()
    move_count = 0
    
    print("Starting position:")
    print(board)
    print()
    
    try:
        while not board.is_game_over() and move_count < 100:  # Limit to 100 moves
            current_bot = white_bot if board.turn == chess.WHITE else black_bot
            
            print(f"Move {move_count + 1}: {current_bot.name} thinking...")
            move = current_bot.get_best_move(board)
            
            if move:
                san_move = board.san(move)
                board.push(move)
                print(f"{current_bot.name} played: {san_move}")
                print(board)
                print()
                move_count += 1
                
                # Add a small delay for readability
                import time
                time.sleep(1)
            else:
                print(f"{current_bot.name} couldn't find a move!")
                break
    
    except KeyboardInterrupt:
        print("\nGame stopped by user.")
    
    # Show final result
    print("\n" + "="*40)
    print("GAME RESULT")
    print("="*40)
    
    if board.is_checkmate():
        winner = "BlackBot" if board.turn == chess.WHITE else "WhiteBot"
        print(f"Checkmate! {winner} wins!")
    elif board.is_stalemate():
        print("Stalemate! Game is a draw.")
    elif board.is_insufficient_material():
        print("Insufficient material! Game is a draw.")
    elif move_count >= 100:
        print("Game ended after 100 moves (limit reached).")
    else:
        print("Game ended.")
    
    print(f"Total moves played: {move_count}")
    print("="*40)

def position_analysis_demo():
    """Demonstrate position analysis on a famous chess position."""
    print("\n" + "="*50)
    print("POSITION ANALYSIS DEMONSTRATION")
    print("="*50)
    print("Analyzing a famous chess position...")

    # Set up a famous position (Immortal Game position)
    board = chess.Board("rnbqkb1r/pppp1ppp/5n2/4p3/2B1P3/8/PPPP1PPP/RNBQK1NR w KQkq - 2 3")

    print("\nPosition after 1.e4 e5 2.Bc4 Nf6:")
    print(format_board_unicode(board))

    # Analyze the position
    analysis = analyze_position(board)
    print(format_analysis(analysis))

    print("\nThis demonstrates the analysis features available during games.")
    print("Type 'analysis' during any game to see similar information.")
    print("="*50)

def main():
    """Main program entry point."""
    print("Welcome to Chess Bot!")
    print("A chess game with AI opponent using minimax algorithm.")
    
    bot_depth = 3  # Default difficulty
    bot_difficulty_name = "Medium"
    
    while True:
        show_main_menu()
        
        try:
            choice = input("Enter your choice (1-6): ").strip()

            if choice == '1':
                # Play against bot
                game = ChessGame()
                game.bot.set_depth(bot_depth)
                print(f"\nStarting game against {game.bot.name} (Difficulty: {bot_difficulty_name})")
                game.play_game()

            elif choice == '2':
                # Bot vs bot
                bot_vs_bot_game()

            elif choice == '3':
                # Set difficulty
                new_depth, new_difficulty = set_difficulty()
                if new_depth is not None:
                    bot_depth = new_depth
                    bot_difficulty_name = new_difficulty
                    print(f"Difficulty set to {bot_difficulty_name} (depth {bot_depth})")

            elif choice == '4':
                # Position analysis demo
                position_analysis_demo()

            elif choice == '5':
                # Help
                show_help()

            elif choice == '6':
                # Quit
                print("Thanks for playing Chess Bot!")
                sys.exit(0)

            else:
                print("Invalid choice. Please enter 1-6.")
        
        except KeyboardInterrupt:
            print("\nExiting Chess Bot. Goodbye!")
            sys.exit(0)
        except Exception as e:
            print(f"An error occurred: {e}")
            print("Please try again.")

if __name__ == "__main__":
    main()
