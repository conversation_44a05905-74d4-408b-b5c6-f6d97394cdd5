# Chess Bot

A complete chess game with AI opponent featuring both command-line and graphical user interfaces.

## Features

### Core Features
- **Full Chess Rules**: Complete implementation of chess rules including castling, en passant, and promotion
- **AI Opponent**: Intelligent bot using minimax algorithm with alpha-beta pruning
- **Multiple Difficulty Levels**: Easy (depth 2) to Expert (depth 5)
- **Position Analysis**: Detailed evaluation of board positions
- **Game Persistence**: Save and load games in standard PGN format
- **Opening Recognition**: Identifies common chess openings
- **Move History**: Complete game notation tracking

### Interface Options
- **CLI Version**: Text-based interface with Unicode chess pieces
- **GUI Version**: Graphical interface with drag-and-drop functionality

## Installation

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Dependencies
```bash
pip install python-chess
```

Note: `tkinter` is required for the GUI version but usually comes pre-installed with Python.

## Quick Start

### Option 1: Use the Launcher (Recommended)
```bash
python launcher.py
```
The launcher will let you choose between CLI and GUI versions and check for dependencies.

### Option 2: Direct Launch
```bash
# CLI Version
python main.py

# GUI Version
python chess_gui.py
```

## Usage

### CLI Version
- Enter moves in algebraic notation (e.g., `e4`, `Nf3`, `O-O`)
- Type `help` during games for move format examples
- Type `moves` to see all legal moves
- Type `analysis` for position evaluation
- Type `quit` to exit

### GUI Version
- Click on pieces to select them
- Click on destination squares to move
- Use the control panel for game options
- Right-click for context menus (future feature)

## File Structure

```
chess_bot_test/
├── launcher.py          # Main launcher (choose CLI/GUI)
├── main.py             # CLI version entry point
├── chess_gui.py        # GUI version entry point
├── chess_game.py       # Core game logic for CLI
├── chess_bot.py        # AI engine implementation
├── utils.py            # Utility functions
├── test_chess_bot.py   # Unit tests
├── requirements.txt    # Python dependencies
└── README.md          # This file
```

## Game Controls

### CLI Version
| Command | Description |
|---------|-------------|
| `e4`, `Nf3`, etc. | Make a move in algebraic notation |
| `e2e4`, `g1f3`, etc. | Make a move in UCI notation |
| `help` | Show move format help |
| `moves` | Display all legal moves |
| `analysis` | Show position analysis |
| `quit` | Exit the game |

### GUI Version
| Action | Description |
|--------|-------------|
| Click piece → Click square | Make a move |
| New Game button | Start a new game |
| Switch Colors button | Change sides |
| Save/Load Game buttons | File operations |
| Difficulty radio buttons | Change bot strength |
| Analyze Position button | Show analysis popup |

## AI Difficulty Levels

| Level | Search Depth | Description |
|-------|-------------|-------------|
| Easy | 2 | Quick moves, basic strategy |
| Medium | 3 | Balanced play (default) |
| Hard | 4 | Strong tactical play |
| Expert | 5 | Very strong play (slower) |

## Features by Version

### CLI Version Features
- ✅ Unicode chess piece display
- ✅ Interactive help system
- ✅ Bot vs Bot demonstration mode
- ✅ Real-time position analysis
- ✅ Opening name display
- ✅ Automatic game saving option
- ✅ Move validation with helpful error messages

### GUI Version Features
- ✅ Visual drag-and-drop interface
- ✅ Real-time board highlighting
- ✅ Legal move indicators
- ✅ Graphical move history
- ✅ File dialogs for save/load
- ✅ Popup position analysis
- ✅ Difficulty selection via radio buttons
- ✅ Threaded bot moves (non-blocking UI)

## Testing

Run the test suite:
```bash
python test_chess_bot.py
```

The tests include:
- Bot functionality tests
- Game logic validation
- Integration tests
- Performance benchmarks

## Technical Details

### AI Algorithm
The chess bot uses:
- **Minimax algorithm** with alpha-beta pruning
- **Position evaluation** based on material and positional factors
- **Move ordering** for better pruning efficiency
- **Configurable search depth** for difficulty adjustment

### Position Evaluation Factors
- Material balance (piece values)
- Piece positioning (position tables)
- Mobility (number of legal moves)
- King safety considerations
- Center control evaluation

## Examples

### Starting a CLI Game
```bash
$ python main.py

==================================================
CHESS BOT - MAIN MENU
==================================================
1. Play against ChessBot
2. Bot vs Bot (watch AI play itself)
3. Set bot difficulty
4. Position analysis demo
5. Help
6. Quit
==================================================
Enter your choice (1-6): 1
```

### Making Moves in CLI
```
Your turn (White)
Enter your move in algebraic notation (e.g., 'e2e4', 'Nf3', 'O-O')
Type 'quit' to exit, 'help' for move format help, 'moves' to see legal moves
Type 'analysis' to see position analysis
Your move: e4
You played: e4

ChessBot is thinking...
ChessBot evaluated 2847 positions in 0.15 seconds
ChessBot played: e5
```

## Troubleshooting

### Common Issues

1. **"Module not found" error**
   - Make sure you're in the correct directory
   - Install dependencies: `pip install python-chess`

2. **GUI won't start**
   - Check if tkinter is installed: `python -c "import tkinter"`
   - On Linux: `sudo apt-get install python3-tk`

3. **Bot moves very slowly**
   - Reduce difficulty level
   - The Expert level (depth 5) can take several seconds per move

4. **Invalid move errors**
   - Use standard algebraic notation (e.g., `Nf3`, not `knight to f3`)
   - Type `help` in CLI for move format examples
   - Type `moves` to see all legal moves

## Contributing

Feel free to contribute improvements:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source. Feel free to use and modify as needed.

## Future Enhancements

Potential improvements:
- [ ] Opening book integration
- [ ] Endgame tablebase support
- [ ] Network play capability
- [ ] Advanced position analysis
- [ ] Custom piece themes for GUI
- [ ] Sound effects
- [ ] Game database management
- [ ] Tournament mode
