PS C:\Users\<USER>\OneDrive - University of Suffolk (1)\projects\AI\Programming AI\chess_bot_test> python chess_gui.py
ChessBot search depth set to 5
ChessBot evaluated 51965 positions in 14.73 seconds
ChessBot evaluated 124939 positions in 44.33 seconds
ChessBot search depth set to 3
ChessBot evaluated 2599 positions in 0.81 seconds
ChessBot evaluated 4425 positions in 1.29 seconds
ChessBot evaluated 3363 positions in 1.00 seconds
ChessBot evaluated 5175 positions in 1.19 seconds
ChessBot evaluated 3494 positions in 0.93 seconds
ChessBot evaluated 3331 positions in 1.04 seconds
ChessBot evaluated 7219 positions in 1.88 seconds
ChessBot evaluated 1232 positions in 0.30 seconds
ChessBot evaluated 2807 positions in 0.92 seconds
ChessBot evaluated 662 positions in 0.19 seconds