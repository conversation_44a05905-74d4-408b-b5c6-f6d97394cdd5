[using augment code]

create a desktop-based GUI version as well that complements the existing CLI version


-------------------------------

optional depending on intent:

[effects of utils.py not showing when AI bot plays against itself]

commit: utils update for Bo<PERSON> vs Bot

-------------------------------

done:

you forgot to create and write the code for utils.py

incorporate utils.py in the main application so everything just works when i run python main.py

commit: utils.py incorporation