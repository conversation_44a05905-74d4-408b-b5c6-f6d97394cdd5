[using augment code]

complete a full game against bot using CLI and save all moves and result as a .pgn

--

Aim: make AI bot make smarter moves, like capturing a free piece that is hanging

[watch vid first to understand the concept]
use monte carlo tree search also (chess bot) -> combine minimax with alpha-beta pruning and MCTS in the chess bot to enhance the bot's decision making process
(leverage the efficient search capabilities of minimax / alpha-beta pruning to find promising moves and then use MCTS to explore those moves in more depth)



AI making silly mistakes (but perhaps this is normal when playing on easy model)

-

include evaluation score (on GUI version)

commit: evaluation scoring included

-

get an LLM to play chess also (as opposed to specialised chess engine or AI algorithms)

--



-------------------------------

optional depending on intent:

[effects of utils.py not showing when AI bot plays against itself in CLI]

commit: utils update for Bot vs Bot


-------------------------------
-------------------------------



done:

you forgot to create and write the code for utils.py

incorporate utils.py in the main application so everything just works when i run python main.py

commit: utils.py incorporation



create a desktop based GUI version as well that complements the existing CLI version

commit: GUI version added with Launcher