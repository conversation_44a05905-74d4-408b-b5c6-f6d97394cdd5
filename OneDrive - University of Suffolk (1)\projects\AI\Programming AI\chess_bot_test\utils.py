#!/usr/bin/env python3
"""
Utility functions for the chess bot application.
"""

import chess
import chess.pgn
import json
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime

def format_board_unicode(board: chess.Board) -> str:
    """
    Format the chess board with Unicode chess pieces for better visualization.
    
    Args:
        board: Chess board to format
        
    Returns:
        Formatted board string with Unicode pieces
    """
    # Unicode chess pieces
    piece_symbols = {
        'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White pieces
        'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black pieces
    }
    
    board_str = str(board)
    
    # Replace ASCII pieces with Unicode
    for ascii_piece, unicode_piece in piece_symbols.items():
        board_str = board_str.replace(ascii_piece, unicode_piece)
    
    # Add coordinates
    lines = board_str.split('\n')
    formatted_lines = []
    
    formatted_lines.append("  ┌─────────────────┐")
    for i, line in enumerate(lines):
        rank = 8 - i
        formatted_line = f"{rank} │ {' '.join(line)} │ {rank}"
        formatted_lines.append(formatted_line)
    formatted_lines.append("  └─────────────────┘")
    formatted_lines.append("    a b c d e f g h")
    
    return '\n'.join(formatted_lines)

def parse_move_input(move_input: str, board: chess.Board) -> Optional[chess.Move]:
    """
    Parse various move input formats and return a chess.Move object.
    
    Args:
        move_input: User input string
        board: Current board position
        
    Returns:
        Parsed move or None if invalid
    """
    move_input = move_input.strip()
    
    try:
        # Try standard algebraic notation first
        return board.parse_san(move_input)
    except ValueError:
        pass
    
    try:
        # Try UCI notation (e.g., e2e4)
        return chess.Move.from_uci(move_input)
    except ValueError:
        pass
    
    # Try some common variations
    move_input_lower = move_input.lower()
    
    # Handle castling variations
    if move_input_lower in ['0-0', 'o-o', 'castle', 'castles']:
        try:
            return board.parse_san('O-O')
        except ValueError:
            pass
    
    if move_input_lower in ['0-0-0', 'o-o-o', 'castle long', 'queenside']:
        try:
            return board.parse_san('O-O-O')
        except ValueError:
            pass
    
    return None

def get_move_quality_description(evaluation: int) -> str:
    """
    Get a human-readable description of move quality based on evaluation.
    
    Args:
        evaluation: Position evaluation score
        
    Returns:
        Description string
    """
    if evaluation > 500:
        return "Winning advantage"
    elif evaluation > 200:
        return "Large advantage"
    elif evaluation > 100:
        return "Clear advantage"
    elif evaluation > 50:
        return "Slight advantage"
    elif evaluation > -50:
        return "Equal position"
    elif evaluation > -100:
        return "Slight disadvantage"
    elif evaluation > -200:
        return "Clear disadvantage"
    elif evaluation > -500:
        return "Large disadvantage"
    else:
        return "Losing position"

def save_game_pgn(board: chess.Board, game_history: List[str], 
                  filename: str = None, white_player: str = "Human", 
                  black_player: str = "ChessBot") -> str:
    """
    Save the game in PGN format.
    
    Args:
        board: Final board position
        game_history: List of moves in SAN notation
        filename: Output filename (auto-generated if None)
        white_player: Name of white player
        black_player: Name of black player
        
    Returns:
        Filename where game was saved
    """
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"chess_game_{timestamp}.pgn"
    
    # Create a new game
    game = chess.pgn.Game()
    
    # Set headers
    game.headers["Event"] = "Chess Bot Game"
    game.headers["Site"] = "Local Computer"
    game.headers["Date"] = datetime.now().strftime("%Y.%m.%d")
    game.headers["Round"] = "1"
    game.headers["White"] = white_player
    game.headers["Black"] = black_player
    
    # Determine result
    if board.is_checkmate():
        if board.turn == chess.WHITE:
            game.headers["Result"] = "0-1"  # Black wins
        else:
            game.headers["Result"] = "1-0"  # White wins
    elif board.is_stalemate() or board.is_insufficient_material():
        game.headers["Result"] = "1/2-1/2"  # Draw
    else:
        game.headers["Result"] = "*"  # Ongoing/unknown
    
    # Add moves
    node = game
    temp_board = chess.Board()
    
    for move_san in game_history:
        try:
            move = temp_board.parse_san(move_san)
            node = node.add_variation(move)
            temp_board.push(move)
        except ValueError:
            # Skip invalid moves
            continue
    
    # Save to file
    with open(filename, 'w') as f:
        print(game, file=f)
    
    return filename

def load_game_pgn(filename: str) -> Tuple[chess.Board, List[str]]:
    """
    Load a game from PGN format.
    
    Args:
        filename: PGN file to load
        
    Returns:
        Tuple of (final_board_position, move_history)
    """
    with open(filename, 'r') as f:
        game = chess.pgn.read_game(f)
    
    if game is None:
        raise ValueError("Could not parse PGN file")
    
    board = game.board()
    move_history = []
    
    for move in game.mainline_moves():
        move_san = board.san(move)
        move_history.append(move_san)
        board.push(move)
    
    return board, move_history

def analyze_position(board: chess.Board) -> Dict:
    """
    Analyze the current position and return various metrics.
    
    Args:
        board: Chess board to analyze
        
    Returns:
        Dictionary with analysis results
    """
    analysis = {
        'material_balance': 0,
        'piece_count': {'white': {}, 'black': {}},
        'mobility': {'white': 0, 'black': 0},
        'king_safety': {'white': 0, 'black': 0},
        'center_control': {'white': 0, 'black': 0}
    }
    
    # Piece values
    piece_values = {
        chess.PAWN: 1, chess.KNIGHT: 3, chess.BISHOP: 3,
        chess.ROOK: 5, chess.QUEEN: 9, chess.KING: 0
    }
    
    # Count pieces and calculate material
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            color_key = 'white' if piece.color == chess.WHITE else 'black'
            piece_type = piece.piece_type
            
            if piece_type not in analysis['piece_count'][color_key]:
                analysis['piece_count'][color_key][piece_type] = 0
            analysis['piece_count'][color_key][piece_type] += 1
            
            value = piece_values[piece_type]
            if piece.color == chess.WHITE:
                analysis['material_balance'] += value
            else:
                analysis['material_balance'] -= value
    
    # Calculate mobility (number of legal moves)
    if board.turn == chess.WHITE:
        analysis['mobility']['white'] = len(list(board.legal_moves))
        board.push(chess.Move.null())  # Switch turns
        analysis['mobility']['black'] = len(list(board.legal_moves))
        board.pop()
    else:
        analysis['mobility']['black'] = len(list(board.legal_moves))
        board.push(chess.Move.null())  # Switch turns
        analysis['mobility']['white'] = len(list(board.legal_moves))
        board.pop()
    
    # Center control (simplified - count pieces attacking center squares)
    center_squares = [chess.D4, chess.D5, chess.E4, chess.E5]
    for square in center_squares:
        white_attackers = len(board.attackers(chess.WHITE, square))
        black_attackers = len(board.attackers(chess.BLACK, square))
        analysis['center_control']['white'] += white_attackers
        analysis['center_control']['black'] += black_attackers
    
    return analysis

def format_analysis(analysis: Dict) -> str:
    """
    Format position analysis for display.
    
    Args:
        analysis: Analysis dictionary from analyze_position
        
    Returns:
        Formatted analysis string
    """
    lines = []
    lines.append("Position Analysis:")
    lines.append("=" * 30)
    
    # Material balance
    material = analysis['material_balance']
    if material > 0:
        lines.append(f"Material: White +{material}")
    elif material < 0:
        lines.append(f"Material: Black +{abs(material)}")
    else:
        lines.append("Material: Equal")
    
    # Piece counts
    white_pieces = analysis['piece_count']['white']
    black_pieces = analysis['piece_count']['black']
    
    piece_names = {
        chess.PAWN: 'Pawns', chess.KNIGHT: 'Knights', chess.BISHOP: 'Bishops',
        chess.ROOK: 'Rooks', chess.QUEEN: 'Queens', chess.KING: 'Kings'
    }
    
    lines.append("\nPiece Count:")
    for piece_type, name in piece_names.items():
        white_count = white_pieces.get(piece_type, 0)
        black_count = black_pieces.get(piece_type, 0)
        lines.append(f"  {name}: White {white_count}, Black {black_count}")
    
    # Mobility
    white_mobility = analysis['mobility']['white']
    black_mobility = analysis['mobility']['black']
    lines.append(f"\nMobility: White {white_mobility}, Black {black_mobility}")
    
    # Center control
    white_center = analysis['center_control']['white']
    black_center = analysis['center_control']['black']
    lines.append(f"Center Control: White {white_center}, Black {black_center}")
    
    return '\n'.join(lines)

def get_opening_name(board: chess.Board, moves: List[str]) -> str:
    """
    Try to identify the opening based on the first few moves.
    
    Args:
        board: Current board position
        moves: List of moves played
        
    Returns:
        Opening name or "Unknown Opening"
    """
    # Simple opening recognition based on first few moves
    if len(moves) < 2:
        return "Opening"
    
    first_moves = ' '.join(moves[:4])  # First 2 moves for each side
    
    opening_patterns = {
        'e4 e5': 'King\'s Pawn Opening',
        'e4 e5 Nf3': 'King\'s Knight Opening',
        'e4 e5 Nf3 Nc6': 'Italian Game / Spanish Opening',
        'e4 e5 Nf3 Nc6 Bb5': 'Spanish Opening (Ruy Lopez)',
        'e4 e5 Nf3 Nc6 Bc4': 'Italian Game',
        'e4 c5': 'Sicilian Defense',
        'd4 d5': 'Queen\'s Pawn Opening',
        'd4 Nf6': 'Indian Defense',
        'Nf3 d5': 'Réti Opening',
        'Nf3 Nf6': 'Réti Opening',
        'c4': 'English Opening',
        'f4': 'Bird\'s Opening',
        'e4 e6': 'French Defense',
        'e4 c6': 'Caro-Kann Defense',
        'd4 d5 c4': 'Queen\'s Gambit'
    }
    
    for pattern, name in opening_patterns.items():
        if first_moves.startswith(pattern):
            return name
    
    return "Unknown Opening"
